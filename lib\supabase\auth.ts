import { createSupabaseClient, createSupabaseServerClient } from './client';
import type { Database } from './types';

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: 'STUDENT' | 'INSTRUCTOR' | 'ADMIN';
  avatar?: string;
  bio?: string;
  skills?: string[];
  interests?: string[];
  isOnboarded: boolean;
}

export interface SignUpData {
  email: string;
  password: string;
  name: string;
  role?: 'STUDENT' | 'INSTRUCTOR' | 'ADMIN';
}

export interface SignInData {
  email: string;
  password: string;
}

export class SupabaseAuthService {
  private supabase = createSupabaseClient();

  async signUp(data: SignUpData): Promise<{ user: AuthUser | null; error: string | null; success: boolean }> {
    try {
      // Call the registration API route
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          name: data.name,
          role: data.role || 'STUDENT'
        }),
      });

      const result = await response.json();

      if (!result.success) {
        return { user: null, error: result.error, success: false };
      }

      // Now sign in the user to get the session
      const { data: authData, error: authError } = await this.supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password
      });

      if (authError) {
        return { user: null, error: authError.message, success: false };
      }

      return {
        user: result.user,
        error: null,
        success: true
      };
    } catch (error: any) {
      return { user: null, error: error.message, success: false };
    }
  }

  async signIn(data: SignInData): Promise<{ user: AuthUser | null; error: string | null; success: boolean }> {
    try {
      const { data: authData, error: authError } = await this.supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password
      });

      if (authError) {
        return { user: null, error: authError.message, success: false };
      }

      if (!authData.user) {
        return { user: null, error: 'Failed to sign in', success: false };
      }

      // Get user profile
      const { data: userData, error: userError } = await this.supabase
        .from('users')
        .select('*')
        .eq('id', authData.user.id)
        .single();

      if (userError) {
        return { user: null, error: userError.message, success: false };
      }

      return {
        user: {
          id: userData.id,
          email: userData.email,
          name: userData.name,
          role: userData.role,
          avatar: userData.avatar,
          bio: userData.bio,
          skills: userData.skills,
          interests: userData.interests,
          is_onboarded: userData.is_onboarded
        },
        error: null,
        success: true
      };
    } catch (error: any) {
      return { user: null, error: error.message, success: false };
    }
  }

  async signOut(): Promise<{ error: string | null }> {
    try {
      const { error } = await this.supabase.auth.signOut();
      return { error: error?.message || null };
    } catch (error: any) {
      return { error: error.message };
    }
  }

  async getCurrentUser(): Promise<{ user: AuthUser | null; error: string | null }> {
    try {
      const { data: authData, error: authError } = await this.supabase.auth.getUser();

      if (authError || !authData.user) {
        return { user: null, error: authError?.message || 'No user found' };
      }

      // Get user profile
      const { data: userData, error: userError } = await this.supabase
        .from('users')
        .select('*')
        .eq('id', authData.user.id)
        .single();

      if (userError) {
        return { user: null, error: userError.message };
      }

      return {
        user: {
          id: userData.id,
          email: userData.email,
          name: userData.name,
          role: userData.role,
          avatar: userData.avatar,
          bio: userData.bio,
          skills: userData.skills,
          interests: userData.interests,
          isOnboarded: userData.is_onboarded
        },
        error: null
      };
    } catch (error: any) {
      return { user: null, error: error.message };
    }
  }

  async updateProfile(userId: string, updates: Partial<AuthUser>): Promise<{ user: AuthUser | null; error: string | null }> {
    try {
      const { data: userData, error: userError } = await this.supabase
        .from('users')
        .update({
          name: updates.name,
          avatar: updates.avatar,
          bio: updates.bio,
          skills: updates.skills,
          interests: updates.interests,
          is_onboarded: updates.isOnboarded,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (userError) {
        return { user: null, error: userError.message };
      }

      return {
        user: {
          id: userData.id,
          email: userData.email,
          name: userData.name,
          role: userData.role,
          avatar: userData.avatar,
          bio: userData.bio,
          skills: userData.skills,
          interests: userData.interests,
          isOnboarded: userData.is_onboarded
        },
        error: null
      };
    } catch (error: any) {
      return { user: null, error: error.message };
    }
  }

  // Admin-specific methods
  async createAdminUser(data: SignUpData): Promise<{ user: AuthUser | null; error: string | null }> {
    return this.signUp({ ...data, role: 'ADMIN' });
  }

  async isAdmin(userId: string): Promise<boolean> {
    try {
      const { data } = await this.supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      return data?.role === 'ADMIN';
    } catch {
      return false;
    }
  }
}

export const authService = new SupabaseAuthService();
